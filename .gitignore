# Dependencies
node_modules/
*/node_modules/
__pycache__/
*.pyc
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
dist/
*.egg-info/

# Temporary upload files
temp_uploads/
*/temp_uploads/

# Testing artifacts
.pytest_cache/
*/.pytest_cache/
coverage.xml
htmlcov/
.coverage*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Virtual environments
venv/
env/
.venv/
*/.venv/

# Docker
*.dockerfile
Dockerfile.dev

# Documentation temp files
*.md:Zone.Identifier

# Setup scripts
setup-*.sh

# Test coverage
.coverage
coverage.xml
*/.coverage
*/coverage.xml

# Temporary uploads
temp_uploads/
*/temp_uploads/

# Conversion outputs (generated files)
conversion_outputs/
*/conversion_outputs/

# API Keys
opencode.json