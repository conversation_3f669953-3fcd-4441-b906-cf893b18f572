name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: |
          ## What's Changed
          
          ### Features
          - New conversion capabilities
          - Enhanced smart assumptions
          - Improved user interface
          
          ### Bug Fixes
          - Various stability improvements
          - Better error handling
          
          ### Documentation
          - Updated API documentation
          - Enhanced user guides
          
          **Full Changelog**: https://github.com/${{ github.repository }}/compare/v1.0.0...${{ github.ref }}
        draft: false
        prerelease: false