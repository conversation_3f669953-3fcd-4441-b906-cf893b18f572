version: 2
updates:
  # Frontend npm dependencies
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "frontend"
    open-pull-requests-limit: 10

  # Root npm workspace
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "workspace"
    open-pull-requests-limit: 10

  # Backend Python dependencies
  - package-ecosystem: "pip"
    directory: "/backend"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "backend"
      - "python"
    open-pull-requests-limit: 10

  # AI Engine Python dependencies
  - package-ecosystem: "pip"
    directory: "/ai-engine"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "ai-engine"
      - "python"
    open-pull-requests-limit: 10

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "docker"
    open-pull-requests-limit: 10

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "github-actions"
    open-pull-requests-limit: 10