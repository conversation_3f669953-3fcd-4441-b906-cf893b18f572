{"name": "modporter-ai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "storybook:test": "test-storybook", "storybook:build": "storybook build --output-dir dist-storybook"}, "dependencies": {"axios": "^1.6.0", "mermaid": "^11.8.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-router-dom": "^6.20.0"}, "devDependencies": {"@storybook/addon-docs": "^9.0.15", "@storybook/addon-onboarding": "^9.0.15", "@storybook/react-vite": "^9.0.15", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/node": "^20.8.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.1.0", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-storybook": "^9.0.15", "jsdom": "^23.0.0", "prettier": "^3.0.0", "storybook": "^9.0.15", "typescript": "^5.2.0", "vite": "^5.0.0", "vitest": "^0.34.6"}}