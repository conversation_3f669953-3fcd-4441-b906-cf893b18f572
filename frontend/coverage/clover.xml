<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751756494426" clover="3.2.0">
  <project timestamp="1751756494426" name="All files">
    <metrics statements="355" coveredstatements="259" conditionals="19" coveredconditionals="14" methods="9" coveredmethods="4" elements="383" coveredelements="277" complexity="0" loc="355" ncloc="355" packages="2" files="2" classes="2"/>
    <package name="components.ConversionUpload">
      <metrics statements="299" coveredstatements="203" conditionals="19" coveredconditionals="14" methods="9" coveredmethods="4"/>
      <file name="ConversionUpload.tsx" path="/home/<USER>/ModPorter-AI/frontend/src/components/ConversionUpload/ConversionUpload.tsx">
        <metrics statements="299" coveredstatements="203" conditionals="19" coveredconditionals="14" methods="9" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="17" count="94" type="stmt"/>
        <line num="18" count="94" type="stmt"/>
        <line num="19" count="94" type="stmt"/>
        <line num="20" count="94" type="stmt"/>
        <line num="21" count="94" type="stmt"/>
        <line num="22" count="94" type="stmt"/>
        <line num="23" count="94" type="stmt"/>
        <line num="24" count="94" type="stmt"/>
        <line num="25" count="94" type="stmt"/>
        <line num="26" count="94" type="stmt"/>
        <line num="27" count="94" type="stmt"/>
        <line num="28" count="94" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="94" type="stmt"/>
        <line num="38" count="94" type="stmt"/>
        <line num="39" count="94" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="168" type="stmt"/>
        <line num="41" count="168" type="stmt"/>
        <line num="42" count="168" type="stmt"/>
        <line num="43" count="168" type="stmt"/>
        <line num="44" count="168" type="stmt"/>
        <line num="45" count="168" type="stmt"/>
        <line num="46" count="168" type="stmt"/>
        <line num="47" count="168" type="stmt"/>
        <line num="48" count="168" type="stmt"/>
        <line num="49" count="168" type="cond" truecount="1" falsecount="0"/>
        <line num="50" count="168" type="cond" truecount="1" falsecount="0"/>
        <line num="51" count="32" type="stmt"/>
        <line num="52" count="32" type="stmt"/>
        <line num="53" count="168" type="stmt"/>
        <line num="54" count="94" type="stmt"/>
        <line num="55" count="94" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="94" type="stmt"/>
        <line num="75" count="94" type="stmt"/>
        <line num="76" count="94" type="stmt"/>
        <line num="77" count="94" type="stmt"/>
        <line num="78" count="94" type="stmt"/>
        <line num="79" count="94" type="stmt"/>
        <line num="80" count="94" type="stmt"/>
        <line num="81" count="94" type="stmt"/>
        <line num="82" count="94" type="stmt"/>
        <line num="83" count="94" type="stmt"/>
        <line num="84" count="94" type="stmt"/>
        <line num="85" count="94" type="cond" truecount="1" falsecount="0"/>
        <line num="86" count="84" type="stmt"/>
        <line num="87" count="84" type="stmt"/>
        <line num="88" count="84" type="stmt"/>
        <line num="89" count="84" type="cond" truecount="1" falsecount="0"/>
        <line num="90" count="53" type="stmt"/>
        <line num="91" count="84" type="cond" truecount="1" falsecount="0"/>
        <line num="92" count="31" type="stmt"/>
        <line num="93" count="31" type="stmt"/>
        <line num="94" count="31" type="stmt"/>
        <line num="95" count="84" type="stmt"/>
        <line num="96" count="94" type="stmt"/>
        <line num="97" count="94" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="94" type="stmt"/>
        <line num="131" count="94" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="94" type="stmt"/>
        <line num="133" count="94" type="stmt"/>
        <line num="134" count="94" type="stmt"/>
        <line num="135" count="94" type="stmt"/>
        <line num="136" count="94" type="stmt"/>
        <line num="137" count="94" type="stmt"/>
        <line num="138" count="94" type="stmt"/>
        <line num="139" count="94" type="stmt"/>
        <line num="140" count="94" type="stmt"/>
        <line num="141" count="94" type="stmt"/>
        <line num="142" count="94" type="stmt"/>
        <line num="143" count="94" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="94" type="stmt"/>
        <line num="145" count="94" type="stmt"/>
        <line num="146" count="94" type="stmt"/>
        <line num="147" count="94" type="cond" truecount="0" falsecount="1"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="94" type="stmt"/>
        <line num="166" count="94" type="stmt"/>
        <line num="167" count="94" type="stmt"/>
        <line num="168" count="94" type="stmt"/>
        <line num="169" count="94" type="stmt"/>
        <line num="170" count="94" type="stmt"/>
        <line num="171" count="94" type="stmt"/>
        <line num="172" count="94" type="stmt"/>
        <line num="173" count="94" type="stmt"/>
        <line num="174" count="94" type="stmt"/>
        <line num="175" count="94" type="stmt"/>
        <line num="176" count="94" type="stmt"/>
        <line num="177" count="94" type="stmt"/>
        <line num="178" count="94" type="stmt"/>
        <line num="179" count="94" type="stmt"/>
        <line num="180" count="94" type="stmt"/>
        <line num="181" count="94" type="stmt"/>
        <line num="182" count="94" type="stmt"/>
        <line num="183" count="94" type="stmt"/>
        <line num="184" count="94" type="stmt"/>
        <line num="185" count="94" type="stmt"/>
        <line num="186" count="94" type="stmt"/>
        <line num="187" count="94" type="stmt"/>
        <line num="188" count="94" type="stmt"/>
        <line num="189" count="94" type="stmt"/>
        <line num="190" count="94" type="stmt"/>
        <line num="191" count="94" type="stmt"/>
        <line num="192" count="94" type="stmt"/>
        <line num="193" count="94" type="stmt"/>
        <line num="194" count="94" type="stmt"/>
        <line num="195" count="94" type="stmt"/>
        <line num="196" count="94" type="stmt"/>
        <line num="197" count="94" type="stmt"/>
        <line num="198" count="94" type="stmt"/>
        <line num="199" count="94" type="stmt"/>
        <line num="200" count="94" type="stmt"/>
        <line num="201" count="94" type="stmt"/>
        <line num="202" count="94" type="stmt"/>
        <line num="203" count="94" type="stmt"/>
        <line num="204" count="94" type="stmt"/>
        <line num="205" count="94" type="stmt"/>
        <line num="206" count="94" type="stmt"/>
        <line num="207" count="94" type="stmt"/>
        <line num="208" count="94" type="stmt"/>
        <line num="209" count="94" type="stmt"/>
        <line num="210" count="94" type="cond" truecount="1" falsecount="0"/>
        <line num="211" count="94" type="stmt"/>
        <line num="212" count="94" type="stmt"/>
        <line num="213" count="94" type="stmt"/>
        <line num="214" count="94" type="stmt"/>
        <line num="215" count="94" type="stmt"/>
        <line num="216" count="94" type="stmt"/>
        <line num="217" count="94" type="stmt"/>
        <line num="218" count="94" type="stmt"/>
        <line num="219" count="94" type="stmt"/>
        <line num="220" count="94" type="stmt"/>
        <line num="221" count="94" type="cond" truecount="1" falsecount="0"/>
        <line num="222" count="1" type="stmt"/>
        <line num="223" count="1" type="stmt"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="1" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="1" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="231" count="94" type="stmt"/>
        <line num="232" count="94" type="stmt"/>
        <line num="233" count="94" type="stmt"/>
        <line num="234" count="94" type="stmt"/>
        <line num="235" count="94" type="stmt"/>
        <line num="236" count="94" type="stmt"/>
        <line num="237" count="94" type="stmt"/>
        <line num="238" count="94" type="stmt"/>
        <line num="239" count="94" type="stmt"/>
        <line num="240" count="94" type="stmt"/>
        <line num="241" count="94" type="stmt"/>
        <line num="242" count="94" type="stmt"/>
        <line num="243" count="94" type="stmt"/>
        <line num="244" count="94" type="stmt"/>
        <line num="245" count="94" type="stmt"/>
        <line num="246" count="94" type="stmt"/>
        <line num="247" count="94" type="stmt"/>
        <line num="248" count="94" type="stmt"/>
        <line num="249" count="94" type="stmt"/>
        <line num="250" count="94" type="stmt"/>
        <line num="251" count="94" type="stmt"/>
        <line num="252" count="94" type="cond" truecount="1" falsecount="0"/>
        <line num="253" count="53" type="stmt"/>
        <line num="254" count="53" type="stmt"/>
        <line num="255" count="53" type="stmt"/>
        <line num="256" count="53" type="stmt"/>
        <line num="257" count="94" type="stmt"/>
        <line num="258" count="94" type="stmt"/>
        <line num="259" count="94" type="stmt"/>
        <line num="260" count="94" type="stmt"/>
        <line num="261" count="94" type="cond" truecount="2" falsecount="0"/>
        <line num="262" count="94" type="stmt"/>
        <line num="263" count="94" type="stmt"/>
        <line num="264" count="94" type="stmt"/>
        <line num="265" count="94" type="cond" truecount="0" falsecount="1"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="94" type="stmt"/>
        <line num="274" count="94" type="stmt"/>
        <line num="275" count="94" type="stmt"/>
        <line num="276" count="94" type="stmt"/>
        <line num="277" count="94" type="stmt"/>
        <line num="278" count="94" type="stmt"/>
        <line num="279" count="94" type="stmt"/>
        <line num="280" count="94" type="stmt"/>
        <line num="281" count="94" type="cond" truecount="0" falsecount="1"/>
        <line num="282" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="94" type="stmt"/>
        <line num="297" count="94" type="stmt"/>
        <line num="298" count="94" type="stmt"/>
        <line num="299" count="94" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="56" coveredstatements="56" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="api.ts" path="/home/<USER>/ModPorter-AI/frontend/src/types/api.ts">
        <metrics statements="56" coveredstatements="56" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
