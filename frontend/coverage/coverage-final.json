{"/home/<USER>/ModPorter-AI/frontend/src/components/ConversionUpload/ConversionUpload.tsx": {"path": "/home/<USER>/ModPorter-AI/frontend/src/components/ConversionUpload/ConversionUpload.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 3}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 55}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 68}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 3}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 53}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 45}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 48}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 72}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 32}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 33}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 1}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 20}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 7}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 70}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 43}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 65}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 71}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 58}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 58}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 82}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 35}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 49}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 103}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 45}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 4}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 56}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 97}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 4}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 45}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 0}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 62}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 49}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 26}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 23}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 27}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 21}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 24}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 6}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 4}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 9}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 34}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 69}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 13}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 19}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 5}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 4}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 0}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 79}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 34}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 35}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 80}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 13}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 4}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 34}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 4}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 22}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 4}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 30}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 80}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 13}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 5}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 4}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 26}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 51}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 19}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 9}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 0}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 69}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 11}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 20}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 13}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 43}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 33}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 5}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 5}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 0}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 71}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 31}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 19}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 4}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 35}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 77}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 12}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 21}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 60}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 5}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 4}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 0}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 37}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 35}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 54}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 13}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 5}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 0}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 41}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 66}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 13}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 5}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 0}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 26}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 19}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 0}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 9}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 42}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 27}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 36}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 25}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 27}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 8}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 0}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 69}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 6}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 30}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 49}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 7}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 19}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 73}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 15}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 29}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 5}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 4}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 0}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 88}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 0}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 10}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 39}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 37}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 45}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 71}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 12}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 0}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 30}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 11}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 28}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 106}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 7}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 63}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 8}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 25}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 40}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 47}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 39}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 66}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 96}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 60}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 18}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 20}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 37}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 31}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 36}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 38}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 16}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 13}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 15}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 21}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 16}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 13}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 41}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 49}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 50}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 64}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 60}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 26}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 21}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 16}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 10}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 12}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 0}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 23}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 41}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 33}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 35}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 14}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 8}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 14}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 20}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 24}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 36}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 114}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 31}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 35}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 10}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 8}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 41}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 55}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 14}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 12}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 0}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 35}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 42}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 38}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 44}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 18}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 29}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 40}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 69}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 14}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 47}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 36}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 20}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 37}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 84}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 61}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 13}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 16}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 21}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 18}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 10}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 44}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 74}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 14}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 10}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 40}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 52}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 50}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 18}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 113}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 106}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 108}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 97}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 19}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 18}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 12}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 14}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 0}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 38}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 44}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 18}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 29}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 43}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 72}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 14}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 47}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 32}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 18}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 10}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 44}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 68}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 14}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 14}}, "248": {"start": {"line": 249, "column": 0}, "end": {"line": 249, "column": 12}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 0}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 27}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 17}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 39}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 48}}, "254": {"start": {"line": 255, "column": 0}, "end": {"line": 255, "column": 17}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 14}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 8}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 0}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 28}}, "259": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 13}}, "260": {"start": {"line": 261, "column": 0}, "end": {"line": 261, "column": 69}}, "261": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 31}}, "262": {"start": {"line": 263, "column": 0}, "end": {"line": 263, "column": 30}}, "263": {"start": {"line": 264, "column": 0}, "end": {"line": 264, "column": 7}}, "264": {"start": {"line": 265, "column": 0}, "end": {"line": 265, "column": 25}}, "265": {"start": {"line": 266, "column": 0}, "end": {"line": 266, "column": 12}}, "266": {"start": {"line": 267, "column": 0}, "end": {"line": 267, "column": 43}}, "267": {"start": {"line": 268, "column": 0}, "end": {"line": 268, "column": 25}}, "268": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 61}}, "269": {"start": {"line": 270, "column": 0}, "end": {"line": 270, "column": 51}}, "270": {"start": {"line": 271, "column": 0}, "end": {"line": 271, "column": 18}}, "271": {"start": {"line": 272, "column": 0}, "end": {"line": 272, "column": 13}}, "272": {"start": {"line": 273, "column": 0}, "end": {"line": 273, "column": 13}}, "273": {"start": {"line": 274, "column": 0}, "end": {"line": 274, "column": 12}}, "274": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 33}}, "275": {"start": {"line": 276, "column": 0}, "end": {"line": 276, "column": 13}}, "276": {"start": {"line": 277, "column": 0}, "end": {"line": 277, "column": 10}}, "277": {"start": {"line": 278, "column": 0}, "end": {"line": 278, "column": 15}}, "278": {"start": {"line": 279, "column": 0}, "end": {"line": 279, "column": 0}}, "279": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 52}}, "280": {"start": {"line": 281, "column": 0}, "end": {"line": 281, "column": 24}}, "281": {"start": {"line": 282, "column": 0}, "end": {"line": 282, "column": 42}}, "282": {"start": {"line": 283, "column": 0}, "end": {"line": 283, "column": 39}}, "283": {"start": {"line": 284, "column": 0}, "end": {"line": 284, "column": 47}}, "284": {"start": {"line": 285, "column": 0}, "end": {"line": 285, "column": 51}}, "285": {"start": {"line": 286, "column": 0}, "end": {"line": 286, "column": 16}}, "286": {"start": {"line": 287, "column": 0}, "end": {"line": 287, "column": 32}}, "287": {"start": {"line": 288, "column": 0}, "end": {"line": 288, "column": 47}}, "288": {"start": {"line": 289, "column": 0}, "end": {"line": 289, "column": 52}}, "289": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 16}}, "290": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 32}}, "291": {"start": {"line": 292, "column": 0}, "end": {"line": 292, "column": 47}}, "292": {"start": {"line": 293, "column": 0}, "end": {"line": 293, "column": 52}}, "293": {"start": {"line": 294, "column": 0}, "end": {"line": 294, "column": 16}}, "294": {"start": {"line": 295, "column": 0}, "end": {"line": 295, "column": 14}}, "295": {"start": {"line": 296, "column": 0}, "end": {"line": 296, "column": 8}}, "296": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 10}}, "297": {"start": {"line": 298, "column": 0}, "end": {"line": 298, "column": 4}}, "298": {"start": {"line": 299, "column": 0}, "end": {"line": 299, "column": 2}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 94, "17": 94, "18": 94, "19": 94, "20": 94, "21": 94, "22": 94, "23": 94, "24": 94, "25": 94, "26": 94, "27": 94, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 94, "37": 94, "38": 94, "39": 168, "40": 168, "41": 168, "42": 168, "43": 168, "44": 168, "45": 168, "46": 168, "47": 168, "48": 168, "49": 168, "50": 32, "51": 32, "52": 168, "53": 94, "54": 94, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 94, "74": 94, "75": 94, "76": 94, "77": 94, "78": 94, "79": 94, "80": 94, "81": 94, "82": 94, "83": 94, "84": 94, "85": 84, "86": 84, "87": 84, "88": 84, "89": 53, "90": 84, "91": 31, "92": 31, "93": 31, "94": 84, "95": 94, "96": 94, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 94, "130": 94, "131": 94, "132": 94, "133": 94, "134": 94, "135": 94, "136": 94, "137": 94, "138": 94, "139": 94, "140": 94, "141": 94, "142": 94, "143": 94, "144": 94, "145": 94, "146": 94, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 94, "165": 94, "166": 94, "167": 94, "168": 94, "169": 94, "170": 94, "171": 94, "172": 94, "173": 94, "174": 94, "175": 94, "176": 94, "177": 94, "178": 94, "179": 94, "180": 94, "181": 94, "182": 94, "183": 94, "184": 94, "185": 94, "186": 94, "187": 94, "188": 94, "189": 94, "190": 94, "191": 94, "192": 94, "193": 94, "194": 94, "195": 94, "196": 94, "197": 94, "198": 94, "199": 94, "200": 94, "201": 94, "202": 94, "203": 94, "204": 94, "205": 94, "206": 94, "207": 94, "208": 94, "209": 94, "210": 94, "211": 94, "212": 94, "213": 94, "214": 94, "215": 94, "216": 94, "217": 94, "218": 94, "219": 94, "220": 94, "221": 1, "222": 1, "223": 1, "224": 1, "225": 1, "226": 1, "227": 1, "228": 1, "229": 1, "230": 94, "231": 94, "232": 94, "233": 94, "234": 94, "235": 94, "236": 94, "237": 94, "238": 94, "239": 94, "240": 94, "241": 94, "242": 94, "243": 94, "244": 94, "245": 94, "246": 94, "247": 94, "248": 94, "249": 94, "250": 94, "251": 94, "252": 53, "253": 53, "254": 53, "255": 53, "256": 94, "257": 94, "258": 94, "259": 94, "260": 94, "261": 94, "262": 94, "263": 94, "264": 94, "265": 0, "266": 0, "267": 0, "268": 0, "269": 0, "270": 0, "271": 0, "272": 94, "273": 94, "274": 94, "275": 94, "276": 94, "277": 94, "278": 94, "279": 94, "280": 94, "281": 0, "282": 0, "283": 0, "284": 0, "285": 0, "286": 0, "287": 0, "288": 0, "289": 0, "290": 0, "291": 0, "292": 0, "293": 0, "294": 0, "295": 94, "296": 94, "297": 94, "298": 94}, "branchMap": {"0": {"type": "branch", "line": 16, "loc": {"start": {"line": 16, "column": 65}, "end": {"line": 299, "column": 2}}, "locations": [{"start": {"line": 16, "column": 65}, "end": {"line": 299, "column": 2}}]}, "1": {"type": "branch", "line": 131, "loc": {"start": {"line": 131, "column": 39}, "end": {"line": 131, "column": 74}}, "locations": [{"start": {"line": 131, "column": 39}, "end": {"line": 131, "column": 74}}]}, "2": {"type": "branch", "line": 131, "loc": {"start": {"line": 131, "column": 67}, "end": {"line": 131, "column": 88}}, "locations": [{"start": {"line": 131, "column": 67}, "end": {"line": 131, "column": 88}}]}, "3": {"type": "branch", "line": 143, "loc": {"start": {"line": 143, "column": 31}, "end": {"line": 143, "column": 62}}, "locations": [{"start": {"line": 143, "column": 31}, "end": {"line": 143, "column": 62}}]}, "4": {"type": "branch", "line": 143, "loc": {"start": {"line": 143, "column": 68}, "end": {"line": 143, "column": 101}}, "locations": [{"start": {"line": 143, "column": 68}, "end": {"line": 143, "column": 101}}]}, "5": {"type": "branch", "line": 147, "loc": {"start": {"line": 147, "column": 9}, "end": {"line": 164, "column": 16}}, "locations": [{"start": {"line": 147, "column": 9}, "end": {"line": 164, "column": 16}}]}, "6": {"type": "branch", "line": 221, "loc": {"start": {"line": 221, "column": 11}, "end": {"line": 230, "column": 18}}, "locations": [{"start": {"line": 221, "column": 11}, "end": {"line": 230, "column": 18}}]}, "7": {"type": "branch", "line": 252, "loc": {"start": {"line": 252, "column": 7}, "end": {"line": 256, "column": 14}}, "locations": [{"start": {"line": 252, "column": 7}, "end": {"line": 256, "column": 14}}]}, "8": {"type": "branch", "line": 261, "loc": {"start": {"line": 261, "column": 38}, "end": {"line": 261, "column": 64}}, "locations": [{"start": {"line": 261, "column": 38}, "end": {"line": 261, "column": 64}}]}, "9": {"type": "branch", "line": 261, "loc": {"start": {"line": 261, "column": 51}, "end": {"line": 261, "column": 66}}, "locations": [{"start": {"line": 261, "column": 51}, "end": {"line": 261, "column": 66}}]}, "10": {"type": "branch", "line": 265, "loc": {"start": {"line": 265, "column": 9}, "end": {"line": 272, "column": 13}}, "locations": [{"start": {"line": 265, "column": 9}, "end": {"line": 272, "column": 13}}]}, "11": {"type": "branch", "line": 281, "loc": {"start": {"line": 281, "column": 7}, "end": {"line": 295, "column": 14}}, "locations": [{"start": {"line": 281, "column": 7}, "end": {"line": 295, "column": 14}}]}, "12": {"type": "branch", "line": 39, "loc": {"start": {"line": 39, "column": 22}, "end": {"line": 53, "column": 4}}, "locations": [{"start": {"line": 39, "column": 22}, "end": {"line": 53, "column": 4}}]}, "13": {"type": "branch", "line": 50, "loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}]}, "14": {"type": "branch", "line": 49, "loc": {"start": {"line": 49, "column": 31}, "end": {"line": 49, "column": 67}}, "locations": [{"start": {"line": 49, "column": 31}, "end": {"line": 49, "column": 67}}]}, "15": {"type": "branch", "line": 85, "loc": {"start": {"line": 85, "column": 26}, "end": {"line": 95, "column": 4}}, "locations": [{"start": {"line": 85, "column": 26}, "end": {"line": 95, "column": 4}}]}, "16": {"type": "branch", "line": 89, "loc": {"start": {"line": 89, "column": 34}, "end": {"line": 91, "column": 11}}, "locations": [{"start": {"line": 89, "column": 34}, "end": {"line": 91, "column": 11}}]}, "17": {"type": "branch", "line": 91, "loc": {"start": {"line": 91, "column": 4}, "end": {"line": 94, "column": 5}}, "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 94, "column": 5}}]}, "18": {"type": "branch", "line": 210, "loc": {"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 84}}, "locations": [{"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 84}}]}}, "b": {"0": [94], "1": [84], "2": [31], "3": [0], "4": [0], "5": [0], "6": [1], "7": [53], "8": [63], "9": [31], "10": [0], "11": [0], "12": [168], "13": [32], "14": [420], "15": [84], "16": [53], "17": [31], "18": [1]}, "fnMap": {"0": {"name": "ConversionUpload", "decl": {"start": {"line": 16, "column": 65}, "end": {"line": 299, "column": 2}}, "loc": {"start": {"line": 16, "column": 65}, "end": {"line": 299, "column": 2}}, "line": 16}, "1": {"name": "validateFile", "decl": {"start": {"line": 28, "column": 23}, "end": {"line": 36, "column": 4}}, "loc": {"start": {"line": 28, "column": 23}, "end": {"line": 36, "column": 4}}, "line": 28}, "2": {"name": "validateUrl", "decl": {"start": {"line": 39, "column": 22}, "end": {"line": 53, "column": 4}}, "loc": {"start": {"line": 39, "column": 22}, "end": {"line": 53, "column": 4}}, "line": 39}, "3": {"name": "handleUrlChange", "decl": {"start": {"line": 85, "column": 26}, "end": {"line": 95, "column": 4}}, "loc": {"start": {"line": 85, "column": 26}, "end": {"line": 95, "column": 4}}, "line": 85}, "4": {"name": "handleConvert", "decl": {"start": {"line": 97, "column": 24}, "end": {"line": 129, "column": 4}}, "loc": {"start": {"line": 97, "column": 24}, "end": {"line": 129, "column": 4}}, "line": 97}, "5": {"name": "onClick", "decl": {"start": {"line": 157, "column": 23}, "end": {"line": 160, "column": 16}}, "loc": {"start": {"line": 157, "column": 23}, "end": {"line": 160, "column": 16}}, "line": 157}, "6": {"name": "onChange", "decl": {"start": {"line": 204, "column": 24}, "end": {"line": 204, "column": 69}}, "loc": {"start": {"line": 204, "column": 24}, "end": {"line": 204, "column": 69}}, "line": 204}, "7": {"name": "onClick", "decl": {"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 84}}, "loc": {"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 84}}, "line": 210}, "8": {"name": "onChange", "decl": {"start": {"line": 239, "column": 24}, "end": {"line": 239, "column": 72}}, "loc": {"start": {"line": 239, "column": 24}, "end": {"line": 239, "column": 72}}, "line": 239}}, "f": {"0": 94, "1": 0, "2": 168, "3": 84, "4": 0, "5": 0, "6": 0, "7": 1, "8": 0}}, "/home/<USER>/ModPorter-AI/frontend/src/types/api.ts": {"path": "/home/<USER>/<PERSON>d<PERSON><PERSON>er-AI/frontend/src/types/api.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 3}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 57}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 3}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 14}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 18}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 28}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 31}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 1}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 37}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 23}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 48}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 29}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 32}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 26}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 23}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 33}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 1}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 0}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 31}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 15}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 18}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 43}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 25}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 21}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 1}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 28}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 15}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 17}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 24}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 1}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 0}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 34}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 26}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 28}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 36}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 22}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 1}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 0}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 29}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 15}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 68}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 21}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 19}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 1}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 0}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 33}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 16}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 19}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 17}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 24}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 1}}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1}, "branchMap": {}, "b": {}, "fnMap": {}, "f": {}}}